package com.haihang.model.DTO.outbound.transportReport;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 随车质检单数据导出请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Accessors(chain = true)
public class TransportReportExportRequest {
    
    /**
     * 集团编号（可选）
     */
    private String groupNumber;
    
    /**
     * 客户编号列表（必选）
     */
    private List<String> customerNumbers;
    
    /**
     * 产品类别编号列表（可选）
     */
    private List<String> productCategoryNumbers;
    
    /**
     * 开始日期（可选，格式：yyyy-MM-dd）
     */
    private String startDate;
    
    /**
     * 结束日期（可选，格式：yyyy-MM-dd）
     */
    private String endDate;
    
    /**
     * 是否根据客户区分Excel文件
     */
    private Boolean separateByCustomer;
}
